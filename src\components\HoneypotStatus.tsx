import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Server, 
  Wifi, 
  WifiOff, 
  Settings, 
  Activity, 
  AlertCircle, 
  CheckCircle, 
  Wrench,
  Database,
  Terminal,
  Globe,
  Shield
} from 'lucide-react';

interface Honeypot {
  id: string;
  name: string;
  type: string;
  status: 'online' | 'offline' | 'maintenance';
  ip: string;
  port: number;
  interactions: number;
  threats: number;
  uptime: number;
  framework: string;
  description: string;
}

export const HoneypotStatus = () => {
  const [honeypots] = useState<Honeypot[]>([
    {
      id: 'ssh-01',
      name: 'SSH Honeypot Alpha',
      type: 'SSH',
      status: 'online',
      ip: '*************',
      port: 22,
      interactions: 1247,
      threats: 892,
      uptime: 99.8,
      framework: 'Cowrie',
      description: 'High-interaction SSH/Telnet honeypot for brute force detection'
    },
    {
      id: 'http-01',
      name: 'Web Honeypot Beta',
      type: 'HTTP',
      status: 'online',
      ip: '*************',
      port: 80,
      interactions: 2156,
      threats: 1034,
      uptime: 98.5,
      framework: 'Dionaea',
      description: 'Medium-interaction web server for injection attack analysis'
    },
    {
      id: 'ftp-01',
      name: 'FTP Honeypot Gamma',
      type: 'FTP',
      status: 'online',
      ip: '*************',
      port: 21,
      interactions: 634,
      threats: 445,
      uptime: 99.2,
      framework: 'T-Pot',
      description: 'Low-interaction FTP service for file transfer attack detection'
    },
    {
      id: 'smb-01',
      name: 'SMB Honeypot Delta',
      type: 'SMB',
      status: 'maintenance',
      ip: '*************',
      port: 445,
      interactions: 467,
      threats: 267,
      uptime: 87.3,
      framework: 'T-Pot',
      description: 'File sharing honeypot for lateral movement detection'
    },
    {
      id: 'telnet-01',
      name: 'Telnet Honeypot Epsilon',
      type: 'Telnet',
      status: 'online',
      ip: '*************',
      port: 23,
      interactions: 892,
      threats: 634,
      uptime: 96.7,
      framework: 'Cowrie',
      description: 'Legacy protocol honeypot for IoT attack simulation'
    },
    {
      id: 'dns-01',
      name: 'DNS Honeypot Zeta',
      type: 'DNS',
      status: 'offline',
      ip: '*************',
      port: 53,
      interactions: 234,
      threats: 103,
      uptime: 45.2,
      framework: 'Dionaea',
      description: 'DNS service honeypot for DNS amplification detection'
    }
  ]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <CheckCircle className="h-4 w-4 text-cyber-green" />;
      case 'offline':
        return <AlertCircle className="h-4 w-4 text-cyber-red" />;
      case 'maintenance':
        return <Wrench className="h-4 w-4 text-cyber-orange" />;
      default:
        return <AlertCircle className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-cyber-green text-black';
      case 'offline':
        return 'bg-cyber-red text-white';
      case 'maintenance':
        return 'bg-cyber-orange text-black';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'SSH':
        return <Terminal className="h-4 w-4" />;
      case 'HTTP':
        return <Globe className="h-4 w-4" />;
      case 'FTP':
        return <Database className="h-4 w-4" />;
      case 'SMB':
        return <Server className="h-4 w-4" />;
      case 'Telnet':
        return <Terminal className="h-4 w-4" />;
      case 'DNS':
        return <Wifi className="h-4 w-4" />;
      default:
        return <Shield className="h-4 w-4" />;
    }
  };

  const onlineCount = honeypots.filter(h => h.status === 'online').length;
  const totalInteractions = honeypots.reduce((sum, h) => sum + h.interactions, 0);
  const totalThreats = honeypots.reduce((sum, h) => sum + h.threats, 0);

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border-primary/20 bg-gradient-to-br from-card to-card/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Online Honeypots</p>
                <p className="text-2xl font-bold text-cyber-green">{onlineCount}/{honeypots.length}</p>
              </div>
              <Server className="h-8 w-8 text-cyber-green" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-primary/20 bg-gradient-to-br from-card to-card/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Interactions</p>
                <p className="text-2xl font-bold text-primary">{totalInteractions.toLocaleString()}</p>
              </div>
              <Activity className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-primary/20 bg-gradient-to-br from-card to-card/50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Threats Detected</p>
                <p className="text-2xl font-bold text-cyber-red">{totalThreats.toLocaleString()}</p>
              </div>
              <AlertCircle className="h-8 w-8 text-cyber-red" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Honeypot Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {honeypots.map((honeypot) => (
          <Card key={honeypot.id} className="border-primary/20 hover:shadow-lg transition-all duration-300">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  {getTypeIcon(honeypot.type)}
                  <span className="text-lg">{honeypot.name}</span>
                </CardTitle>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(honeypot.status)}
                  <Badge className={getStatusColor(honeypot.status)}>
                    {honeypot.status.toUpperCase()}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground">{honeypot.description}</p>
              
              {/* Connection Details */}
              <div className="grid grid-cols-2 gap-4 p-3 bg-muted/30 rounded-lg">
                <div>
                  <p className="text-xs text-muted-foreground">IP Address</p>
                  <p className="text-sm font-mono">{honeypot.ip}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Port</p>
                  <p className="text-sm font-mono">{honeypot.port}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Framework</p>
                  <p className="text-sm font-medium">{honeypot.framework}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Type</p>
                  <p className="text-sm font-medium">{honeypot.type}</p>
                </div>
              </div>

              {/* Metrics */}
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Uptime</span>
                  <span className="text-sm font-medium">{honeypot.uptime}%</span>
                </div>
                <Progress value={honeypot.uptime} className="h-2" />

                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-2 bg-muted/20 rounded">
                    <p className="text-lg font-bold text-primary">{honeypot.interactions}</p>
                    <p className="text-xs text-muted-foreground">Interactions</p>
                  </div>
                  <div className="text-center p-2 bg-muted/20 rounded">
                    <p className="text-lg font-bold text-cyber-red">{honeypot.threats}</p>
                    <p className="text-xs text-muted-foreground">Threats</p>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex space-x-2 pt-2">
                <Button variant="outline" size="sm" className="flex-1">
                  <Settings className="h-4 w-4 mr-2" />
                  Configure
                </Button>
                <Button variant="outline" size="sm" className="flex-1">
                  <Activity className="h-4 w-4 mr-2" />
                  Monitor
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};