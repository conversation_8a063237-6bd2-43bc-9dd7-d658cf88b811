import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Target, Users, Database, Shield, AlertTriangle, Activity } from 'lucide-react';

interface MitreTechnique {
  id: string;
  name: string;
  tactic: string;
  description: string;
  detections: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  mitigation: string;
}

export const MitreMatrix = () => {
  const [selectedTactic, setSelectedTactic] = useState('initial-access');

  const tactics = [
    { id: 'initial-access', name: 'Initial Access', icon: Target, color: 'cyber-red' },
    { id: 'execution', name: 'Execution', icon: Activity, color: 'cyber-orange' },
    { id: 'persistence', name: 'Persistence', icon: Database, color: 'warning' },
    { id: 'privilege-escalation', name: 'Privilege Escalation', icon: Users, color: 'cyber-purple' },
    { id: 'defense-evasion', name: 'Defense Evasion', icon: Shield, color: 'cyber-blue' },
    { id: 'credential-access', name: 'Credential Access', icon: AlertTriangle, color: 'cyber-green' }
  ];

  const techniques: Record<string, MitreTechnique[]> = {
    'initial-access': [
      {
        id: 'T1078',
        name: 'Valid Accounts',
        tactic: 'Initial Access',
        description: 'Adversaries may obtain and abuse credentials of existing accounts',
        detections: 234,
        severity: 'high',
        mitigation: 'Multi-factor authentication, account monitoring'
      },
      {
        id: 'T1190',
        name: 'Exploit Public-Facing Application',
        tactic: 'Initial Access',
        description: 'Adversaries may exploit weaknesses in Internet-facing applications',
        detections: 167,
        severity: 'critical',
        mitigation: 'Regular patching, web application firewall'
      },
      {
        id: 'T1566',
        name: 'Phishing',
        tactic: 'Initial Access',
        description: 'Adversaries may send phishing messages to gain access',
        detections: 89,
        severity: 'medium',
        mitigation: 'Email filtering, user training'
      }
    ],
    'execution': [
      {
        id: 'T1059',
        name: 'Command and Scripting Interpreter',
        tactic: 'Execution',
        description: 'Adversaries may abuse command interpreters to execute commands',
        detections: 445,
        severity: 'high',
        mitigation: 'Script execution policies, monitoring'
      },
      {
        id: 'T1569',
        name: 'System Services',
        tactic: 'Execution',
        description: 'Adversaries may abuse system services to execute commands',
        detections: 278,
        severity: 'medium',
        mitigation: 'Service monitoring, least privilege'
      }
    ],
    'persistence': [
      {
        id: 'T1053',
        name: 'Scheduled Task/Job',
        tactic: 'Persistence',
        description: 'Adversaries may abuse task scheduling to maintain persistence',
        detections: 156,
        severity: 'medium',
        mitigation: 'Task monitoring, access controls'
      },
      {
        id: 'T1547',
        name: 'Boot or Logon Autostart Execution',
        tactic: 'Persistence',
        description: 'Adversaries may configure system settings to automatically execute',
        detections: 203,
        severity: 'high',
        mitigation: 'Registry monitoring, startup program control'
      }
    ]
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-cyber-red';
      case 'high': return 'bg-cyber-orange';
      case 'medium': return 'bg-warning';
      case 'low': return 'bg-cyber-green';
      default: return 'bg-muted';
    }
  };

  const getTacticColor = (color: string) => {
    return `text-${color}`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-6 w-6 text-primary" />
            <span>MITRE ATT&CK Framework Integration</span>
          </CardTitle>
          <p className="text-muted-foreground">
            Real-time mapping of detected attack patterns to MITRE tactics and techniques
          </p>
        </CardHeader>
      </Card>

      {/* Tactics Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {tactics.map((tactic) => {
          const IconComponent = tactic.icon;
          const isSelected = selectedTactic === tactic.id;
          const tacticTechniques = techniques[tactic.id] || [];
          const totalDetections = tacticTechniques.reduce((sum, t) => sum + t.detections, 0);
          
          return (
            <Card 
              key={tactic.id}
              className={`cursor-pointer transition-all duration-300 hover:shadow-lg ${
                isSelected ? 'border-primary shadow-cyber' : 'border-border/50'
              }`}
              onClick={() => setSelectedTactic(tactic.id)}
            >
              <CardContent className="p-4 text-center">
                <IconComponent className={`h-8 w-8 mx-auto mb-2 ${getTacticColor(tactic.color)}`} />
                <h3 className="font-medium text-sm mb-1">{tactic.name}</h3>
                <p className="text-lg font-bold text-primary">{totalDetections}</p>
                <p className="text-xs text-muted-foreground">detections</p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Selected Tactic Details */}
      <Card className="border-primary/20">
        <CardHeader>
          <CardTitle>
            {tactics.find(t => t.id === selectedTactic)?.name} - Techniques Detected
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {(techniques[selectedTactic] || []).map((technique) => (
              <Card key={technique.id} className="border-border/50 bg-muted/20">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <Badge variant="outline" className="font-mono text-xs">
                          {technique.id}
                        </Badge>
                        <Badge className={`${getSeverityColor(technique.severity)} text-black font-medium`}>
                          {technique.severity.toUpperCase()}
                        </Badge>
                      </div>
                      <h4 className="font-semibold text-lg mb-2">{technique.name}</h4>
                      <p className="text-sm text-muted-foreground mb-3">
                        {technique.description}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-2xl font-bold text-cyber-red">{technique.detections}</p>
                      <p className="text-xs text-muted-foreground">detections</p>
                    </div>
                  </div>
                  
                  <div className="bg-card/50 p-3 rounded-lg border border-border/30">
                    <h5 className="text-sm font-medium mb-1 text-cyber-green">Recommended Mitigation:</h5>
                    <p className="text-sm text-muted-foreground">{technique.mitigation}</p>
                  </div>
                  
                  <div className="flex justify-between items-center mt-3 pt-3 border-t border-border/30">
                    <Button variant="outline" size="sm">
                      View Details
                    </Button>
                    <Button variant="cyber" size="sm">
                      Generate Report
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          {(!techniques[selectedTactic] || techniques[selectedTactic].length === 0) && (
            <div className="text-center py-8">
              <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
              <p className="text-muted-foreground">No techniques detected for this tactic</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Attack Timeline */}
      <Card className="border-primary/20">
        <CardHeader>
          <CardTitle>Attack Chain Analysis</CardTitle>
          <p className="text-sm text-muted-foreground">
            Timeline of detected attack progression through MITRE tactics
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 overflow-x-auto pb-4">
            {tactics.map((tactic, index) => {
              const IconComponent = tactic.icon;
              const hasDetections = techniques[tactic.id] && techniques[tactic.id].length > 0;
              
              return (
                <div key={tactic.id} className="flex items-center space-x-4 min-w-max">
                  <div className={`flex flex-col items-center p-3 rounded-lg border ${
                    hasDetections ? 'border-cyber-red bg-cyber-red/10' : 'border-border/50 bg-muted/20'
                  }`}>
                    <IconComponent className={`h-6 w-6 mb-1 ${
                      hasDetections ? 'text-cyber-red' : 'text-muted-foreground'
                    }`} />
                    <span className="text-xs text-center font-medium">{tactic.name}</span>
                    {hasDetections && (
                      <Badge className="mt-1 bg-cyber-red text-white text-xs">
                        Active
                      </Badge>
                    )}
                  </div>
                  
                  {index < tactics.length - 1 && (
                    <div className={`w-8 h-0.5 ${
                      hasDetections ? 'bg-cyber-red' : 'bg-border'
                    }`} />
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};