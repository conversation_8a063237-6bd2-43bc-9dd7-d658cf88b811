import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Database, 
  Search, 
  Filter, 
  Download, 
  AlertTriangle, 
  Activity, 
  Terminal,
  Eye,
  Zap,
  Shield,
  Settings
} from 'lucide-react';

interface LogEntry {
  id: string;
  timestamp: string;
  source: string;
  level: 'INFO' | 'WARN' | 'ERROR' | 'CRITICAL';
  message: string;
  service: string;
  ip?: string;
  details?: Record<string, any>;
}

interface ElkStats {
  totalLogs: number;
  logsPerSecond: number;
  indexSize: string;
  errors: number;
  warnings: number;
}

export const LogAnalyzer = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedService, setSelectedService] = useState('all');
  const [logEntries, setLogEntries] = useState<LogEntry[]>([]);
  const [elkStats, setElkStats] = useState<ElkStats>({
    totalLogs: 2847392,
    logsPerSecond: 47.3,
    indexSize: '24.7 GB',
    errors: 1247,
    warnings: 3456
  });

  const services = ['all', 'cowrie', 'dionaea', 'snort', 'suricata', 'zeek', 'filebeat', 'logstash'];

  useEffect(() => {
    // Simulate real-time log entries
    const generateLogEntry = (): LogEntry => {
      const sources = ['SSH-HP-01', 'HTTP-HP-02', 'FTP-HP-03', 'SMB-HP-04', 'NIDS-01', 'NIDS-02'];
      const services = ['cowrie', 'dionaea', 'snort', 'suricata', 'zeek'];
      const levels: Array<'INFO' | 'WARN' | 'ERROR' | 'CRITICAL'> = ['INFO', 'WARN', 'ERROR', 'CRITICAL'];
      const messages = [
        'SSH connection attempt from unknown IP',
        'Malware sample uploaded to honeypot',
        'Port scan detected from external source',
        'SQL injection attempt blocked',
        'Suspicious file download activity',
        'Brute force login attempts detected',
        'Network traffic anomaly identified',
        'DDoS attack pattern recognized'
      ];

      return {
        id: Math.random().toString(36).substr(2, 9),
        timestamp: new Date().toISOString(),
        source: sources[Math.floor(Math.random() * sources.length)],
        level: levels[Math.floor(Math.random() * levels.length)],
        message: messages[Math.floor(Math.random() * messages.length)],
        service: services[Math.floor(Math.random() * services.length)],
        ip: `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
        details: {
          port: Math.floor(Math.random() * 65535),
          protocol: ['TCP', 'UDP'][Math.floor(Math.random() * 2)],
          bytes: Math.floor(Math.random() * 10000)
        }
      };
    };

    // Initial log entries
    const initialLogs = Array.from({ length: 20 }, generateLogEntry);
    setLogEntries(initialLogs);

    // Simulate real-time updates
    const interval = setInterval(() => {
      setLogEntries(prev => [generateLogEntry(), ...prev.slice(0, 49)]);
      setElkStats(prev => ({
        ...prev,
        totalLogs: prev.totalLogs + Math.floor(Math.random() * 10),
        logsPerSecond: 40 + Math.random() * 20
      }));
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const filteredLogs = logEntries.filter(log => {
    const matchesSearch = searchQuery === '' || 
      log.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
      log.source.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (log.ip && log.ip.includes(searchQuery));
    
    const matchesService = selectedService === 'all' || log.service === selectedService;
    
    return matchesSearch && matchesService;
  });

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'CRITICAL': return 'bg-cyber-red text-white';
      case 'ERROR': return 'bg-cyber-orange text-black';
      case 'WARN': return 'bg-warning text-black';
      case 'INFO': return 'bg-cyber-blue text-black';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'CRITICAL': return <AlertTriangle className="h-3 w-3" />;
      case 'ERROR': return <AlertTriangle className="h-3 w-3" />;
      case 'WARN': return <Eye className="h-3 w-3" />;
      case 'INFO': return <Activity className="h-3 w-3" />;
      default: return <Activity className="h-3 w-3" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* ELK Stack Status */}
      <Card className="border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Database className="h-6 w-6 text-primary" />
            <span>ELK Stack - Centralized Logging & SIEM</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-primary">{elkStats.totalLogs.toLocaleString()}</p>
              <p className="text-xs text-muted-foreground">Total Logs</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-cyber-green">{elkStats.logsPerSecond.toFixed(1)}</p>
              <p className="text-xs text-muted-foreground">Logs/Second</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-cyber-blue">{elkStats.indexSize}</p>
              <p className="text-xs text-muted-foreground">Index Size</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-cyber-red">{elkStats.errors}</p>
              <p className="text-xs text-muted-foreground">Errors</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-cyber-orange">{elkStats.warnings}</p>
              <p className="text-xs text-muted-foreground">Warnings</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="live-logs" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="live-logs">Live Logs</TabsTrigger>
          <TabsTrigger value="kibana-dashboard">Kibana Dashboard</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="live-logs" className="space-y-6">
          {/* Search and Filter Controls */}
          <Card className="border-primary/20">
            <CardContent className="p-4">
              <div className="flex space-x-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search logs... (IP, message, source)"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <select
                  value={selectedService}
                  onChange={(e) => setSelectedService(e.target.value)}
                  className="px-3 py-2 bg-background border border-border rounded-md text-sm"
                >
                  {services.map(service => (
                    <option key={service} value={service}>
                      {service === 'all' ? 'All Services' : service.toUpperCase()}
                    </option>
                  ))}
                </select>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  Filters
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Live Log Stream */}
          <Card className="border-primary/20">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  <Terminal className="h-5 w-5 text-primary" />
                  <span>Live Log Stream</span>
                </CardTitle>
                <div className="flex items-center space-x-2">
                  <Zap className="h-4 w-4 text-cyber-green animate-cyber-pulse" />
                  <span className="text-sm text-cyber-green">Real-time</span>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-96 overflow-y-auto font-mono text-sm">
                {filteredLogs.map((log) => (
                  <div key={log.id} className="flex items-start space-x-3 p-2 rounded bg-muted/20 border border-border/30 hover:bg-muted/40 transition-colors">
                    <Badge className={`${getLevelColor(log.level)} font-semibold text-xs flex items-center space-x-1`}>
                      {getLevelIcon(log.level)}
                      <span>{log.level}</span>
                    </Badge>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-xs text-muted-foreground">
                          {new Date(log.timestamp).toLocaleString()}
                        </span>
                        <Badge variant="outline" className="text-xs">
                          {log.service}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {log.source}
                        </Badge>
                        {log.ip && (
                          <Badge variant="outline" className="text-xs font-mono">
                            {log.ip}
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm">{log.message}</p>
                      {log.details && (
                        <div className="text-xs text-muted-foreground mt-1">
                          Port: {log.details.port} | Protocol: {log.details.protocol} | Bytes: {log.details.bytes}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
              
              {filteredLogs.length === 0 && (
                <div className="text-center py-8">
                  <Search className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                  <p className="text-muted-foreground">No logs match your search criteria</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="kibana-dashboard">
          <Card className="border-primary/20">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="h-5 w-5 text-primary" />
                <span>Kibana Visualization Dashboard</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Attack Types Chart */}
                <Card className="border-border/50">
                  <CardHeader>
                    <CardTitle className="text-base">Attack Types Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-48 bg-gradient-to-b from-secondary/20 to-secondary/40 rounded-lg border border-border/50 flex items-center justify-center">
                      <div className="text-center">
                        <Activity className="h-12 w-12 text-primary/50 mx-auto mb-2" />
                        <p className="text-sm text-muted-foreground">Interactive Pie Chart</p>
                        <p className="text-xs text-muted-foreground">SSH Brute Force: 45%</p>
                        <p className="text-xs text-muted-foreground">Port Scans: 25%</p>
                        <p className="text-xs text-muted-foreground">Web Attacks: 20%</p>
                        <p className="text-xs text-muted-foreground">Other: 10%</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Traffic Volume */}
                <Card className="border-border/50">
                  <CardHeader>
                    <CardTitle className="text-base">Traffic Volume Over Time</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-48 bg-gradient-to-b from-secondary/20 to-secondary/40 rounded-lg border border-border/50 flex items-center justify-center">
                      <div className="text-center">
                        <Activity className="h-12 w-12 text-primary/50 mx-auto mb-2" />
                        <p className="text-sm text-muted-foreground">Time Series Chart</p>
                        <p className="text-xs text-muted-foreground">Real-time traffic visualization</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Geographic Heatmap */}
                <Card className="border-border/50">
                  <CardHeader>
                    <CardTitle className="text-base">Geographic Attack Heatmap</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-48 bg-gradient-to-b from-secondary/20 to-secondary/40 rounded-lg border border-border/50 flex items-center justify-center">
                      <div className="text-center">
                        <Activity className="h-12 w-12 text-primary/50 mx-auto mb-2" />
                        <p className="text-sm text-muted-foreground">World Map Heatmap</p>
                        <p className="text-xs text-muted-foreground">Attack origin visualization</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Top Attackers */}
                <Card className="border-border/50">
                  <CardHeader>
                    <CardTitle className="text-base">Top Attack Sources</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {[
                        { ip: '**************', country: 'Russia', attacks: 847 },
                        { ip: '***************', country: 'China', attacks: 623 },
                        { ip: '*************', country: 'North Korea', attacks: 432 },
                        { ip: '*************', country: 'Iran', attacks: 298 }
                      ].map((attacker, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <div>
                            <p className="font-mono text-sm">{attacker.ip}</p>
                            <p className="text-xs text-muted-foreground">{attacker.country}</p>
                          </div>
                          <Badge className="bg-cyber-red text-white">{attacker.attacks}</Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts">
          <Card className="border-primary/20">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-cyber-red" />
                <span>Security Alerts & Notifications</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  {
                    id: 1,
                    severity: 'CRITICAL',
                    title: 'Multiple Failed Login Attempts',
                    description: 'Detected 50+ failed SSH login attempts from ************** in the last 5 minutes',
                    timestamp: '2 minutes ago',
                    status: 'Active'
                  },
                  {
                    id: 2,
                    severity: 'HIGH',
                    title: 'Malware Upload Detected',
                    description: 'Suspicious file uploaded to FTP honeypot, matches known malware signature',
                    timestamp: '8 minutes ago',
                    status: 'Investigating'
                  },
                  {
                    id: 3,
                    severity: 'MEDIUM',
                    title: 'Port Scan Activity',
                    description: 'Comprehensive port scan detected from ***************',
                    timestamp: '15 minutes ago',
                    status: 'Resolved'
                  }
                ].map((alert) => (
                  <Card key={alert.id} className="border-border/50">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <Badge className={`${getLevelColor(alert.severity)} font-semibold`}>
                            {alert.severity}
                          </Badge>
                          <Badge variant="outline">{alert.status}</Badge>
                        </div>
                        <span className="text-xs text-muted-foreground">{alert.timestamp}</span>
                      </div>
                      <h4 className="font-semibold mb-1">{alert.title}</h4>
                      <p className="text-sm text-muted-foreground mb-3">{alert.description}</p>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">Acknowledge</Button>
                        <Button variant="cyber" size="sm">Investigate</Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card className="border-primary/20">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5 text-primary" />
                <span>Log Analytics & Insights</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card className="border-border/50">
                  <CardHeader>
                    <CardTitle className="text-base">Log Volume Trends</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm">Today</span>
                        <span className="font-semibold text-cyber-green">+15%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">This Week</span>
                        <span className="font-semibold text-cyber-orange">+8%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">This Month</span>
                        <span className="font-semibold text-cyber-red">+25%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-border/50">
                  <CardHeader>
                    <CardTitle className="text-base">Service Performance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Elasticsearch</span>
                        <Badge className="bg-cyber-green text-black">Healthy</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Logstash</span>
                        <Badge className="bg-cyber-green text-black">Healthy</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Kibana</span>
                        <Badge className="bg-cyber-green text-black">Healthy</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Filebeat</span>
                        <Badge className="bg-warning text-black">Warning</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-border/50">
                  <CardHeader>
                    <CardTitle className="text-base">Data Retention</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm">Hot Storage</span>
                        <span className="text-sm font-medium">7 days</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Warm Storage</span>
                        <span className="text-sm font-medium">30 days</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Cold Storage</span>
                        <span className="text-sm font-medium">1 year</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};