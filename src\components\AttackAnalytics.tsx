import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON>, TrendingUp, Clock, Target } from 'lucide-react';
import { Progress } from '@/components/ui/progress';

export const AttackAnalytics = () => {
  const attackTypes = [
    { name: 'SSH Brute Force', count: 1247, percentage: 35, trend: '+12%' },
    { name: 'Port Scanning', count: 892, percentage: 25, trend: '+8%' },
    { name: 'HTTP Injection', count: 634, percentage: 18, trend: '-3%' },
    { name: 'FTP Exploitation', count: 445, percentage: 12, trend: '+15%' },
    { name: 'SMB Attacks', count: 267, percentage: 7, trend: '+5%' },
    { name: 'Other', count: 103, percentage: 3, trend: '+2%' }
  ];

  const hourlyStats = [
    { hour: '00:00', attacks: 45 },
    { hour: '02:00', attacks: 32 },
    { hour: '04:00', attacks: 28 },
    { hour: '06:00', attacks: 67 },
    { hour: '08:00', attacks: 89 },
    { hour: '10:00', attacks: 134 },
    { hour: '12:00', attacks: 167 },
    { hour: '14:00', attacks: 198 },
    { hour: '16:00', attacks: 156 },
    { hour: '18:00', attacks: 134 },
    { hour: '20:00', attacks: 112 },
    { hour: '22:00', attacks: 78 }
  ];

  const maxAttacks = Math.max(...hourlyStats.map(stat => stat.attacks));

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Attack Types Distribution */}
      <Card className="border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart className="h-5 w-5 text-primary" />
            <span>Attack Type Distribution</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {attackTypes.map((attack) => (
              <div key={attack.name} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">{attack.name}</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-muted-foreground">{attack.count}</span>
                    <span className={`text-xs font-medium ${
                      attack.trend.startsWith('+') ? 'text-cyber-green' : 'text-cyber-red'
                    }`}>
                      {attack.trend}
                    </span>
                  </div>
                </div>
                <Progress value={attack.percentage} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Hourly Attack Patterns */}
      <Card className="border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Clock className="h-5 w-5 text-primary" />
            <span>24-Hour Attack Pattern</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Simple bar chart visualization */}
            <div className="grid grid-cols-12 gap-1 h-32 items-end">
              {hourlyStats.map((stat, index) => (
                <div key={index} className="flex flex-col items-center space-y-1">
                  <div
                    className="w-full bg-primary rounded-t transition-all duration-300 hover:bg-primary/80"
                    style={{
                      height: `${(stat.attacks / maxAttacks) * 100}%`,
                      minHeight: '4px'
                    }}
                    title={`${stat.hour}: ${stat.attacks} attacks`}
                  />
                  <span className="text-xs text-muted-foreground rotate-45 origin-left">
                    {stat.hour.split(':')[0]}
                  </span>
                </div>
              ))}
            </div>
            
            {/* Peak hours info */}
            <div className="grid grid-cols-3 gap-4 pt-4 border-t border-border/50">
              <div className="text-center">
                <p className="text-sm font-medium text-cyber-red">Peak Hour</p>
                <p className="text-lg font-bold">14:00</p>
                <p className="text-xs text-muted-foreground">198 attacks</p>
              </div>
              <div className="text-center">
                <p className="text-sm font-medium text-cyber-green">Lowest Hour</p>
                <p className="text-lg font-bold">04:00</p>
                <p className="text-xs text-muted-foreground">28 attacks</p>
              </div>
              <div className="text-center">
                <p className="text-sm font-medium text-primary">Average</p>
                <p className="text-lg font-bold">103</p>
                <p className="text-xs text-muted-foreground">per hour</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};