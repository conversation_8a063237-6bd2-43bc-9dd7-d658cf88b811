import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Cpu, 
  HardDrive, 
  MemoryStick, 
  Thermometer, 
  Zap, 
  Monitor,
  Download,
  Activity,
  Server,
  Gauge
} from 'lucide-react';

interface SystemInfo {
  cpu: {
    model: string;
    cores: number;
    threads: number;
    usage: number;
    frequency: number;
    temperature: number;
  };
  memory: {
    total: number;
    used: number;
    available: number;
    usage: number;
  };
  disk: {
    total: number;
    used: number;
    available: number;
    usage: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    packetsIn: number;
    packetsOut: number;
  };
  uptime: number;
  processes: number;
}

export const SystemMonitor = () => {
  const [systemInfo, setSystemInfo] = useState<SystemInfo>({
    cpu: {
      model: 'Intel Core i7-12700K',
      cores: 12,
      threads: 20,
      usage: 0,
      frequency: 3600,
      temperature: 0
    },
    memory: {
      total: 32768,
      used: 0,
      available: 0,
      usage: 0
    },
    disk: {
      total: 1024,
      used: 0,
      available: 0,
      usage: 0
    },
    network: {
      bytesIn: 0,
      bytesOut: 0,
      packetsIn: 0,
      packetsOut: 0
    },
    uptime: 0,
    processes: 0
  });

  const [isExporting, setIsExporting] = useState(false);

  useEffect(() => {
    // Simulate real-time system monitoring
    const interval = setInterval(() => {
      setSystemInfo(prev => ({
        cpu: {
          ...prev.cpu,
          usage: Math.max(5, Math.min(95, prev.cpu.usage + (Math.random() - 0.5) * 10)),
          temperature: Math.max(35, Math.min(85, 45 + Math.random() * 20)),
          frequency: 3600 + Math.random() * 400
        },
        memory: {
          ...prev.memory,
          used: Math.max(8192, Math.min(28672, prev.memory.used + (Math.random() - 0.5) * 1024)),
          get available() { return prev.memory.total - this.used; },
          get usage() { return (this.used / prev.memory.total) * 100; }
        },
        disk: {
          ...prev.disk,
          used: Math.max(256, Math.min(900, prev.disk.used + (Math.random() - 0.5) * 10)),
          get available() { return prev.disk.total - this.used; },
          get usage() { return (this.used / prev.disk.total) * 100; }
        },
        network: {
          bytesIn: prev.network.bytesIn + Math.floor(Math.random() * 1000000),
          bytesOut: prev.network.bytesOut + Math.floor(Math.random() * 500000),
          packetsIn: prev.network.packetsIn + Math.floor(Math.random() * 1000),
          packetsOut: prev.network.packetsOut + Math.floor(Math.random() * 800)
        },
        uptime: prev.uptime + 1,
        processes: Math.max(150, Math.min(300, prev.processes + Math.floor((Math.random() - 0.5) * 10)))
      }));
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}d ${hours}h ${minutes}m`;
  };

  const exportSystemData = async () => {
    setIsExporting(true);
    
    const exportData = {
      timestamp: new Date().toISOString(),
      systemInfo,
      exportType: 'system_monitor',
      metadata: {
        version: '1.0.0',
        source: 'Threat Haven Synergy',
        format: 'JSON'
      }
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
      type: 'application/json' 
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `system_monitor_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    setTimeout(() => setIsExporting(false), 1000);
  };

  return (
    <div className="space-y-6">
      {/* Header with Export */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Monitor className="h-6 w-6 text-primary" />
          <h2 className="text-2xl font-bold">System Monitor</h2>
        </div>
        <Button 
          onClick={exportSystemData} 
          disabled={isExporting}
          className="flex items-center space-x-2"
        >
          <Download className="h-4 w-4" />
          <span>{isExporting ? 'Exporting...' : 'Export Data'}</span>
        </Button>
      </div>

      {/* CPU Information */}
      <Card className="border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Cpu className="h-5 w-5 text-primary" />
            <span>Processor Information</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Model</p>
              <p className="text-lg font-semibold">{systemInfo.cpu.model}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Cores/Threads</p>
              <p className="text-lg font-semibold">{systemInfo.cpu.cores}/{systemInfo.cpu.threads}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Frequency</p>
              <p className="text-lg font-semibold">{systemInfo.cpu.frequency.toFixed(0)} MHz</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Temperature</p>
              <p className="text-lg font-semibold flex items-center space-x-1">
                <Thermometer className="h-4 w-4" />
                <span>{systemInfo.cpu.temperature.toFixed(1)}°C</span>
              </p>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm font-medium">CPU Usage</span>
              <span className="text-sm font-medium">{systemInfo.cpu.usage.toFixed(1)}%</span>
            </div>
            <Progress 
              value={systemInfo.cpu.usage} 
              className="h-3"
            />
          </div>
        </CardContent>
      </Card>

      {/* Memory and Storage */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="border-primary/20">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MemoryStick className="h-5 w-5 text-cyber-blue" />
              <span>Memory Usage</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-sm text-muted-foreground">Total</p>
                <p className="text-lg font-semibold">{formatBytes(systemInfo.memory.total * 1024 * 1024)}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Used</p>
                <p className="text-lg font-semibold text-cyber-orange">{formatBytes(systemInfo.memory.used * 1024 * 1024)}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Available</p>
                <p className="text-lg font-semibold text-cyber-green">{formatBytes(systemInfo.memory.available * 1024 * 1024)}</p>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Memory Usage</span>
                <span className="text-sm font-medium">{systemInfo.memory.usage.toFixed(1)}%</span>
              </div>
              <Progress value={systemInfo.memory.usage} className="h-3" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-primary/20">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <HardDrive className="h-5 w-5 text-cyber-purple" />
              <span>Disk Usage</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-sm text-muted-foreground">Total</p>
                <p className="text-lg font-semibold">{formatBytes(systemInfo.disk.total * 1024 * 1024 * 1024)}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Used</p>
                <p className="text-lg font-semibold text-cyber-orange">{formatBytes(systemInfo.disk.used * 1024 * 1024 * 1024)}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Available</p>
                <p className="text-lg font-semibold text-cyber-green">{formatBytes(systemInfo.disk.available * 1024 * 1024 * 1024)}</p>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Disk Usage</span>
                <span className="text-sm font-medium">{systemInfo.disk.usage.toFixed(1)}%</span>
              </div>
              <Progress value={systemInfo.disk.usage} className="h-3" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Network and System Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="border-primary/20">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5 text-cyber-green" />
              <span>Network Activity</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Bytes In</p>
                <p className="text-lg font-semibold text-cyber-green">{formatBytes(systemInfo.network.bytesIn)}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Bytes Out</p>
                <p className="text-lg font-semibold text-cyber-blue">{formatBytes(systemInfo.network.bytesOut)}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Packets In</p>
                <p className="text-lg font-semibold">{systemInfo.network.packetsIn.toLocaleString()}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Packets Out</p>
                <p className="text-lg font-semibold">{systemInfo.network.packetsOut.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-primary/20">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Server className="h-5 w-5 text-primary" />
              <span>System Statistics</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Uptime</p>
                <p className="text-lg font-semibold">{formatUptime(systemInfo.uptime)}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Processes</p>
                <p className="text-lg font-semibold">{systemInfo.processes}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Load Average</p>
                <p className="text-lg font-semibold">{(systemInfo.cpu.usage / 100 * systemInfo.cpu.cores).toFixed(2)}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Status</p>
                <Badge className="bg-cyber-green text-black font-semibold">
                  <Zap className="h-3 w-3 mr-1" />
                  Online
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
