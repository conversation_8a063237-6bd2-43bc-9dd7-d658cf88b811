@tailwind base;
@tailwind components;
@tailwind utilities;

/* Advanced Cybersecurity Honeypot System Design System */

@layer base {
  :root {
    /* Dark cybersecurity theme */
    --background: 220 27% 8%;
    --foreground: 220 9% 85%;

    --card: 220 25% 10%;
    --card-foreground: 220 9% 85%;

    --popover: 220 25% 10%;
    --popover-foreground: 220 9% 85%;

    /* Cyber blue primary colors */
    --primary: 200 100% 50%;
    --primary-foreground: 220 27% 8%;

    --secondary: 220 25% 15%;
    --secondary-foreground: 220 9% 85%;

    --muted: 220 25% 12%;
    --muted-foreground: 220 9% 65%;

    --accent: 280 100% 60%;
    --accent-foreground: 220 27% 8%;

    /* Threat level colors */
    --destructive: 0 100% 60%;
    --destructive-foreground: 220 27% 8%;
    
    --warning: 45 100% 55%;
    --warning-foreground: 220 27% 8%;
    
    --success: 142 76% 45%;
    --success-foreground: 220 27% 8%;

    --border: 220 25% 18%;
    --input: 220 25% 15%;
    --ring: 200 100% 50%;

    /* Cyber-specific colors */
    --cyber-green: 142 76% 45%;
    --cyber-orange: 25 100% 55%;
    --cyber-red: 0 100% 60%;
    --cyber-purple: 280 100% 60%;
    --cyber-blue: 200 100% 50%;
    
    /* Gradients */
    --gradient-cyber: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
    --gradient-threat: linear-gradient(135deg, hsl(var(--cyber-red)), hsl(var(--cyber-orange)));
    --gradient-safe: linear-gradient(135deg, hsl(var(--cyber-green)), hsl(var(--cyber-blue)));
    
    /* Shadows and glows */
    --shadow-cyber: 0 0 20px hsl(var(--primary) / 0.3);
    --shadow-threat: 0 0 20px hsl(var(--cyber-red) / 0.3);
    --shadow-success: 0 0 20px hsl(var(--cyber-green) / 0.3);
    
    /* Animations */
    --transition-cyber: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}