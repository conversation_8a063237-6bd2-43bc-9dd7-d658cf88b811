import { useState, useEffect, useCallback } from 'react';

interface ExportData {
  timestamp: string;
  threats: any[];
  logs: any[];
  systemMetrics: any;
  networkActivity: any;
}

interface ExportConfig {
  enabled: boolean;
  interval: number; // seconds
  format: 'json' | 'csv' | 'xml';
  destination: 'download' | 'websocket' | 'api';
  maxFileSize: number; // MB
}

interface ExportStats {
  totalExports: number;
  lastExportTime?: Date;
  totalDataExported: number; // bytes
  isExporting: boolean;
  errors: number;
}

export const useRealTimeExport = (initialConfig: Partial<ExportConfig> = {}) => {
  const [config, setConfig] = useState<ExportConfig>({
    enabled: false,
    interval: 300, // 5 minutes
    format: 'json',
    destination: 'download',
    maxFileSize: 10,
    ...initialConfig
  });

  const [stats, setStats] = useState<ExportStats>({
    totalExports: 0,
    totalDataExported: 0,
    isExporting: false,
    errors: 0
  });

  const [exportQueue, setExportQueue] = useState<ExportData[]>([]);

  // Generate mock real-time data
  const generateRealTimeData = useCallback((): ExportData => {
    const now = new Date();
    
    return {
      timestamp: now.toISOString(),
      threats: Array.from({ length: Math.floor(Math.random() * 10) + 1 }, (_, i) => ({
        id: `threat_${now.getTime()}_${i}`,
        timestamp: now.toISOString(),
        severity: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)],
        type: ['SSH Brute Force', 'Port Scan', 'SQL Injection', 'Malware Upload', 'DDoS Attempt'][Math.floor(Math.random() * 5)],
        source: `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
        honeypot: ['SSH-HP-01', 'HTTP-HP-02', 'FTP-HP-03', 'SMB-HP-04'][Math.floor(Math.random() * 4)],
        blocked: Math.random() > 0.3,
        details: {
          port: Math.floor(Math.random() * 65535),
          protocol: ['TCP', 'UDP'][Math.floor(Math.random() * 2)],
          payload: Math.random().toString(36).substring(7)
        }
      })),
      logs: Array.from({ length: Math.floor(Math.random() * 20) + 5 }, (_, i) => ({
        id: `log_${now.getTime()}_${i}`,
        timestamp: now.toISOString(),
        level: ['INFO', 'WARN', 'ERROR', 'CRITICAL'][Math.floor(Math.random() * 4)],
        service: ['cowrie', 'dionaea', 'snort', 'suricata', 'zeek'][Math.floor(Math.random() * 5)],
        message: [
          'SSH connection attempt detected',
          'Malware sample uploaded',
          'Port scan activity identified',
          'SQL injection blocked',
          'Suspicious file access',
          'Network anomaly detected'
        ][Math.floor(Math.random() * 6)],
        source: ['SSH-HP-01', 'HTTP-HP-02', 'FTP-HP-03'][Math.floor(Math.random() * 3)],
        ip: `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`
      })),
      systemMetrics: {
        cpu: {
          usage: Math.random() * 100,
          temperature: 45 + Math.random() * 20,
          frequency: 3600 + Math.random() * 400,
          cores: 12,
          threads: 20
        },
        memory: {
          total: 32768,
          used: 16384 + Math.random() * 8192,
          available: 16384 - Math.random() * 8192,
          usage: 50 + Math.random() * 30
        },
        disk: {
          total: 1024,
          used: 512 + Math.random() * 256,
          available: 512 - Math.random() * 256,
          usage: 50 + Math.random() * 30
        },
        processes: Math.floor(Math.random() * 100) + 150,
        uptime: Math.floor(Math.random() * 86400) + 3600
      },
      networkActivity: {
        bytesIn: Math.floor(Math.random() * 1000000),
        bytesOut: Math.floor(Math.random() * 500000),
        packetsIn: Math.floor(Math.random() * 10000),
        packetsOut: Math.floor(Math.random() * 8000),
        connections: Math.floor(Math.random() * 100) + 50,
        bandwidth: Math.random() * 100
      }
    };
  }, []);

  // Convert data to specified format
  const convertData = useCallback((data: ExportData, format: string): string => {
    switch (format) {
      case 'csv':
        // Convert threats to CSV
        const threatHeaders = 'Timestamp,ID,Severity,Type,Source,Honeypot,Blocked,Port,Protocol';
        const threatRows = data.threats.map(threat => 
          `"${threat.timestamp}","${threat.id}","${threat.severity}","${threat.type}","${threat.source}","${threat.honeypot}","${threat.blocked}","${threat.details.port}","${threat.details.protocol}"`
        ).join('\n');
        return `${threatHeaders}\n${threatRows}`;
      
      case 'xml':
        return `<?xml version="1.0" encoding="UTF-8"?>
<realTimeData timestamp="${data.timestamp}">
  <threats count="${data.threats.length}">
    ${data.threats.map(threat => `
    <threat>
      <id>${threat.id}</id>
      <timestamp>${threat.timestamp}</timestamp>
      <severity>${threat.severity}</severity>
      <type>${threat.type}</type>
      <source>${threat.source}</source>
      <honeypot>${threat.honeypot}</honeypot>
      <blocked>${threat.blocked}</blocked>
    </threat>`).join('')}
  </threats>
  <systemMetrics>
    <cpu usage="${data.systemMetrics.cpu.usage}" temperature="${data.systemMetrics.cpu.temperature}" />
    <memory usage="${data.systemMetrics.memory.usage}" used="${data.systemMetrics.memory.used}" />
    <disk usage="${data.systemMetrics.disk.usage}" used="${data.systemMetrics.disk.used}" />
  </systemMetrics>
</realTimeData>`;
      
      default:
        return JSON.stringify(data, null, 2);
    }
  }, []);

  // Export data function
  const exportData = useCallback(async (data: ExportData) => {
    setStats(prev => ({ ...prev, isExporting: true }));

    try {
      const content = convertData(data, config.format);
      const blob = new Blob([content], { 
        type: config.format === 'json' ? 'application/json' : 
             config.format === 'csv' ? 'text/csv' : 'application/xml'
      });

      // Check file size
      const fileSizeMB = blob.size / (1024 * 1024);
      if (fileSizeMB > config.maxFileSize) {
        console.warn(`Export file size (${fileSizeMB.toFixed(2)}MB) exceeds limit (${config.maxFileSize}MB)`);
        setStats(prev => ({ ...prev, errors: prev.errors + 1 }));
        return;
      }

      if (config.destination === 'download') {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `realtime_export_${data.timestamp.slice(0, 19).replace(/:/g, '-')}.${config.format}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      } else if (config.destination === 'websocket') {
        // Simulate WebSocket export
        console.log('Exporting via WebSocket:', content.substring(0, 100) + '...');
      } else if (config.destination === 'api') {
        // Simulate API export
        console.log('Exporting via API:', content.substring(0, 100) + '...');
      }

      setStats(prev => ({
        ...prev,
        totalExports: prev.totalExports + 1,
        lastExportTime: new Date(),
        totalDataExported: prev.totalDataExported + blob.size,
        isExporting: false
      }));

    } catch (error) {
      console.error('Export failed:', error);
      setStats(prev => ({ 
        ...prev, 
        errors: prev.errors + 1,
        isExporting: false
      }));
    }
  }, [config, convertData]);

  // Real-time data collection and export
  useEffect(() => {
    if (!config.enabled) return;

    const interval = setInterval(() => {
      const data = generateRealTimeData();
      setExportQueue(prev => [...prev, data]);
    }, 1000); // Collect data every second

    return () => clearInterval(interval);
  }, [config.enabled, generateRealTimeData]);

  // Process export queue
  useEffect(() => {
    if (!config.enabled || exportQueue.length === 0) return;

    const exportInterval = setInterval(() => {
      if (exportQueue.length > 0 && !stats.isExporting) {
        const dataToExport = exportQueue[0];
        setExportQueue(prev => prev.slice(1));
        exportData(dataToExport);
      }
    }, config.interval * 1000);

    return () => clearInterval(exportInterval);
  }, [config.enabled, config.interval, exportQueue, stats.isExporting, exportData]);

  // Manual export function
  const manualExport = useCallback(() => {
    const data = generateRealTimeData();
    exportData(data);
  }, [generateRealTimeData, exportData]);

  // Update configuration
  const updateConfig = useCallback((newConfig: Partial<ExportConfig>) => {
    setConfig(prev => ({ ...prev, ...newConfig }));
  }, []);

  // Reset stats
  const resetStats = useCallback(() => {
    setStats({
      totalExports: 0,
      totalDataExported: 0,
      isExporting: false,
      errors: 0
    });
    setExportQueue([]);
  }, []);

  return {
    config,
    stats,
    exportQueue: exportQueue.length,
    updateConfig,
    manualExport,
    resetStats,
    isActive: config.enabled
  };
};
