import React, { useState, useEffect } from 'react';
import { Shield, Activity, AlertTriangle, Users, Network, Brain, Eye, Database, Terminal, Zap } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ThreatMap } from './ThreatMap';
import { AttackAnalytics } from './AttackAnalytics';
import { HoneypotStatus } from './HoneypotStatus';
import { MitreMatrix } from './MitreMatrix';
import { UserBehaviorAnalytics } from './UserBehaviorAnalytics';
import { LogAnalyzer } from './LogAnalyzer';

export const HoneypotDashboard = () => {
  const [activeThreats, setActiveThreats] = useState(147);
  const [blockedAttacks, setBlockedAttacks] = useState(2839);
  const [realTimeEvents, setRealTimeEvents] = useState<Array<{
    id: string;
    timestamp: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    source: string;
    type: string;
    honeypot: string;
  }>>([]);

  useEffect(() => {
    // Simulate real-time threat events
    const interval = setInterval(() => {
      const threats = ['SSH Brute Force', 'Port Scan', 'SQL Injection', 'Malware Upload', 'DDoS Attempt', 'Privilege Escalation'];
      const honeypots = ['SSH-HP-01', 'HTTP-HP-02', 'FTP-HP-03', 'SMB-HP-04', 'Telnet-HP-05'];
      const severities: Array<'low' | 'medium' | 'high' | 'critical'> = ['low', 'medium', 'high', 'critical'];
      
      const newEvent = {
        id: Math.random().toString(36).substr(2, 9),
        timestamp: new Date().toISOString(),
        severity: severities[Math.floor(Math.random() * severities.length)],
        source: `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
        type: threats[Math.floor(Math.random() * threats.length)],
        honeypot: honeypots[Math.floor(Math.random() * honeypots.length)]
      };

      setRealTimeEvents(prev => [newEvent, ...prev.slice(0, 19)]);
      
      if (Math.random() > 0.7) {
        setActiveThreats(prev => prev + 1);
      }
      if (Math.random() > 0.8) {
        setBlockedAttacks(prev => prev + 1);
      }
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-cyber-red';
      case 'high': return 'bg-cyber-orange';
      case 'medium': return 'bg-warning';
      case 'low': return 'bg-cyber-green';
      default: return 'bg-muted';
    }
  };

  return (
    <div className="min-h-screen bg-background p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Shield className="h-10 w-10 text-primary animate-cyber-pulse" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">Advanced Honeypot System</h1>
            <p className="text-muted-foreground">Educational Cybersecurity Defense Platform</p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Button variant="cyber" size="sm">
            <Terminal className="h-4 w-4 mr-2" />
            Console
          </Button>
          <Button variant="threat" size="sm">
            <AlertTriangle className="h-4 w-4 mr-2" />
            Incident Response
          </Button>
        </div>
      </div>

      {/* Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-primary/20 bg-gradient-to-br from-card to-card/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Threats</CardTitle>
            <Activity className="h-4 w-4 text-cyber-red animate-threat-glow" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-cyber-red">{activeThreats}</div>
            <p className="text-xs text-muted-foreground">+12% from last hour</p>
          </CardContent>
        </Card>

        <Card className="border-primary/20 bg-gradient-to-br from-card to-card/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Blocked Attacks</CardTitle>
            <Shield className="h-4 w-4 text-cyber-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-cyber-green">{blockedAttacks}</div>
            <p className="text-xs text-muted-foreground">+8% from last hour</p>
          </CardContent>
        </Card>

        <Card className="border-primary/20 bg-gradient-to-br from-card to-card/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Honeypots Online</CardTitle>
            <Network className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-primary">15/16</div>
            <p className="text-xs text-muted-foreground">1 maintenance mode</p>
          </CardContent>
        </Card>

        <Card className="border-primary/20 bg-gradient-to-br from-card to-card/50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">ML Accuracy</CardTitle>
            <Brain className="h-4 w-4 text-cyber-purple" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-cyber-purple">94.7%</div>
            <p className="text-xs text-muted-foreground">+2.1% this week</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="honeypots">Honeypots</TabsTrigger>
          <TabsTrigger value="analytics">ML Analytics</TabsTrigger>
          <TabsTrigger value="behavior">User Behavior</TabsTrigger>
          <TabsTrigger value="mitre">MITRE ATT&CK</TabsTrigger>
          <TabsTrigger value="logs">Logs & SIEM</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Real-time Events */}
            <Card className="border-primary/20">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Zap className="h-5 w-5 text-primary" />
                  <span>Real-time Threat Events</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {realTimeEvents.map((event) => (
                    <div key={event.id} className="flex items-center justify-between p-3 rounded-lg bg-muted/50 border border-border/50">
                      <div className="flex items-center space-x-3">
                        <Badge className={`${getSeverityColor(event.severity)} text-black font-medium`}>
                          {event.severity.toUpperCase()}
                        </Badge>
                        <div>
                          <p className="text-sm font-medium">{event.type}</p>
                          <p className="text-xs text-muted-foreground">
                            {event.source} → {event.honeypot}
                          </p>
                        </div>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {new Date(event.timestamp).toLocaleTimeString()}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Threat Geography */}
            <ThreatMap />
          </div>

          <AttackAnalytics />
        </TabsContent>

        <TabsContent value="honeypots">
          <HoneypotStatus />
        </TabsContent>

        <TabsContent value="analytics">
          <div className="space-y-6">
            <Card className="border-primary/20">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Brain className="h-5 w-5 text-cyber-purple" />
                  <span>Machine Learning Engine Status</span>
                </CardTitle>
                <CardDescription>
                  Real-time analysis using CIC-IDS2017, UNSW-NB15, and CTU-13 datasets
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Supervised Classification</span>
                      <span className="text-sm font-medium">96.2%</span>
                    </div>
                    <Progress value={96.2} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Anomaly Detection</span>
                      <span className="text-sm font-medium">91.8%</span>
                    </div>
                    <Progress value={91.8} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Zero-day Detection</span>
                      <span className="text-sm font-medium">87.4%</span>
                    </div>
                    <Progress value={87.4} className="h-2" />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card className="border-border/50">
                    <CardHeader>
                      <CardTitle className="text-base">Attack Classification</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {[
                          { type: 'Brute Force', count: 342, percentage: 45 },
                          { type: 'Port Scan', count: 189, percentage: 25 },
                          { type: 'SQL Injection', count: 97, percentage: 13 },
                          { type: 'DDoS', count: 78, percentage: 10 },
                          { type: 'Other', count: 54, percentage: 7 }
                        ].map((attack) => (
                          <div key={attack.type} className="flex items-center justify-between">
                            <span className="text-sm">{attack.type}</span>
                            <div className="flex items-center space-x-2">
                              <span className="text-sm font-medium">{attack.count}</span>
                              <div className="w-20 bg-muted rounded-full h-2">
                                <div 
                                  className="bg-primary h-2 rounded-full" 
                                  style={{ width: `${attack.percentage}%` }}
                                />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-border/50">
                    <CardHeader>
                      <CardTitle className="text-base">Model Performance</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Precision</span>
                          <span className="text-sm font-medium text-cyber-green">94.7%</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Recall</span>
                          <span className="text-sm font-medium text-cyber-green">92.3%</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">F1-Score</span>
                          <span className="text-sm font-medium text-cyber-green">93.5%</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">False Positives</span>
                          <span className="text-sm font-medium text-cyber-orange">3.2%</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="behavior">
          <UserBehaviorAnalytics />
        </TabsContent>

        <TabsContent value="mitre">
          <MitreMatrix />
        </TabsContent>

        <TabsContent value="logs">
          <LogAnalyzer />
        </TabsContent>
      </Tabs>
    </div>
  );
};