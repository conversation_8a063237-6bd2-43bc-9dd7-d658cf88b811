import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Globe, MapPin } from 'lucide-react';

export const ThreatMap = () => {
  const [threats, setThreats] = useState<Array<{
    id: string;
    country: string;
    ip: string;
    attacks: number;
    severity: 'low' | 'medium' | 'high' | 'critical';
    lat: number;
    lng: number;
  }>>([]);

  useEffect(() => {
    // Simulate threat data from different countries
    const threatData = [
      { id: '1', country: 'Russia', ip: '**************', attacks: 847, severity: 'critical' as const, lat: 55.7558, lng: 37.6176 },
      { id: '2', country: 'China', ip: '***************', attacks: 623, severity: 'high' as const, lat: 39.9042, lng: 116.4074 },
      { id: '3', country: 'North Korea', ip: '*************', attacks: 432, severity: 'high' as const, lat: 39.0392, lng: 125.7625 },
      { id: '4', country: 'Iran', ip: '*************', attacks: 298, severity: 'medium' as const, lat: 35.6892, lng: 51.3890 },
      { id: '5', country: 'Brazil', ip: '*************', attacks: 187, severity: 'medium' as const, lat: -14.2350, lng: -51.9253 },
      { id: '6', country: 'Romania', ip: '*************', attacks: 143, severity: 'low' as const, lat: 45.9432, lng: 24.9668 }
    ];

    setThreats(threatData);
  }, []);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-cyber-red';
      case 'high': return 'text-cyber-orange';
      case 'medium': return 'text-warning';
      case 'low': return 'text-cyber-green';
      default: return 'text-muted-foreground';
    }
  };

  const getSeverityBadgeColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-cyber-red';
      case 'high': return 'bg-cyber-orange';
      case 'medium': return 'bg-warning';
      case 'low': return 'bg-cyber-green';
      default: return 'bg-muted';
    }
  };

  return (
    <Card className="border-primary/20">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Globe className="h-5 w-5 text-primary" />
          <span>Global Threat Origins</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Simulated World Map */}
        <div className="relative w-full h-64 bg-gradient-to-b from-secondary/20 to-secondary/40 rounded-lg border border-border/50 mb-4 overflow-hidden">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <Globe className="h-16 w-16 text-primary/50 mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">Interactive Threat Map</p>
              <p className="text-xs text-muted-foreground">Real-time attack origins visualization</p>
            </div>
          </div>
          
          {/* Simulated threat indicators */}
          {threats.map((threat, index) => (
            <div
              key={threat.id}
              className={`absolute w-3 h-3 rounded-full animate-cyber-pulse ${getSeverityColor(threat.severity)} bg-current`}
              style={{
                top: `${20 + (index * 12)}%`,
                left: `${15 + (index * 13)}%`,
              }}
              title={`${threat.country}: ${threat.attacks} attacks`}
            />
          ))}
        </div>

        {/* Threat List */}
        <div className="space-y-3 max-h-48 overflow-y-auto">
          {threats.map((threat) => (
            <div key={threat.id} className="flex items-center justify-between p-3 rounded-lg bg-muted/30 border border-border/30">
              <div className="flex items-center space-x-3">
                <MapPin className={`h-4 w-4 ${getSeverityColor(threat.severity)}`} />
                <div>
                  <p className="text-sm font-medium">{threat.country}</p>
                  <p className="text-xs text-muted-foreground">{threat.ip}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge className={`${getSeverityBadgeColor(threat.severity)} text-black font-medium`}>
                  {threat.attacks}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {threat.severity.toUpperCase()}
                </Badge>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};