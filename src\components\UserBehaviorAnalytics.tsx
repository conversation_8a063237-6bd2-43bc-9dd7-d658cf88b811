import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  User, 
  Clock, 
  MapPin, 
  Activity, 
  AlertTriangle, 
  TrendingUp, 
  Shield,
  Terminal,
  FileText,
  Calendar
} from 'lucide-react';

interface UserProfile {
  id: string;
  username: string;
  department: string;
  riskScore: number;
  lastSeen: string;
  anomalies: number;
  normalBehavior: {
    loginTimes: string[];
    commonIPs: string[];
    avgSessionDuration: string;
    commonCommands: string[];
  };
  recentActivity: Array<{
    timestamp: string;
    action: string;
    risk: 'low' | 'medium' | 'high';
    details: string;
  }>;
}

export const UserBehaviorAnalytics = () => {
  const [selectedUser, setSelectedUser] = useState<string>('user1');
  const [timeRange, setTimeRange] = useState('24h');

  const users: UserProfile[] = [
    {
      id: 'user1',
      username: 'john.doe',
      department: 'Engineering',
      riskScore: 85,
      lastSeen: '2 minutes ago',
      anomalies: 7,
      normalBehavior: {
        loginTimes: ['9:00-10:00', '13:00-14:00', '17:00-18:00'],
        commonIPs: ['************', '**********'],
        avgSessionDuration: '4h 32m',
        commonCommands: ['git', 'npm', 'docker', 'ssh']
      },
      recentActivity: [
        {
          timestamp: '2024-01-23 14:35:21',
          action: 'Unusual login time',
          risk: 'high',
          details: 'Login at 02:30 AM - outside normal hours'
        },
        {
          timestamp: '2024-01-23 14:22:15',
          action: 'New IP address',
          risk: 'medium',
          details: 'Login from ************ - first time seen'
        },
        {
          timestamp: '2024-01-23 13:45:33',
          action: 'Privilege escalation attempt',
          risk: 'high',
          details: 'sudo su command executed'
        },
        {
          timestamp: '2024-01-23 12:15:44',
          action: 'File access pattern',
          risk: 'medium',
          details: 'Accessed 15 files in /etc/ directory'
        }
      ]
    },
    {
      id: 'user2',
      username: 'sarah.smith',
      department: 'Marketing',
      riskScore: 23,
      lastSeen: '1 hour ago',
      anomalies: 1,
      normalBehavior: {
        loginTimes: ['8:30-9:30', '12:00-13:00'],
        commonIPs: ['************'],
        avgSessionDuration: '6h 15m',
        commonCommands: ['ls', 'cat', 'vim', 'grep']
      },
      recentActivity: [
        {
          timestamp: '2024-01-23 11:30:15',
          action: 'Normal login',
          risk: 'low',
          details: 'Regular morning login pattern'
        },
        {
          timestamp: '2024-01-23 10:45:22',
          action: 'File download',
          risk: 'low',
          details: 'Downloaded marketing_report.pdf'
        }
      ]
    },
    {
      id: 'user3',
      username: 'mike.admin',
      department: 'IT Security',
      riskScore: 92,
      lastSeen: '5 minutes ago',
      anomalies: 12,
      normalBehavior: {
        loginTimes: ['24/7 - System Administrator'],
        commonIPs: ['************', '************'],
        avgSessionDuration: '8h+',
        commonCommands: ['sudo', 'systemctl', 'netstat', 'ps']
      },
      recentActivity: [
        {
          timestamp: '2024-01-23 14:40:11',
          action: 'Multiple failed logins',
          risk: 'high',
          details: '8 failed SSH attempts from different IPs'
        },
        {
          timestamp: '2024-01-23 14:25:33',
          action: 'Suspicious command sequence',
          risk: 'high',
          details: 'Executed network reconnaissance commands'
        },
        {
          timestamp: '2024-01-23 13:55:17',
          action: 'Database access',
          risk: 'medium',
          details: 'Accessed user credential database'
        }
      ]
    }
  ];

  const selectedUserData = users.find(u => u.id === selectedUser) || users[0];

  const getRiskColor = (score: number) => {
    if (score >= 80) return 'text-cyber-red';
    if (score >= 60) return 'text-cyber-orange';
    if (score >= 40) return 'text-warning';
    return 'text-cyber-green';
  };

  const getRiskBadgeColor = (risk: string) => {
    switch (risk) {
      case 'high': return 'bg-cyber-red';
      case 'medium': return 'bg-cyber-orange';
      case 'low': return 'bg-cyber-green';
      default: return 'bg-muted';
    }
  };

  const getDepartmentIcon = (dept: string) => {
    switch (dept) {
      case 'Engineering': return <Terminal className="h-4 w-4" />;
      case 'Marketing': return <TrendingUp className="h-4 w-4" />;
      case 'IT Security': return <Shield className="h-4 w-4" />;
      default: return <User className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">User Behavior Analytics</h2>
          <p className="text-muted-foreground">Insider threat detection through behavioral analysis</p>
        </div>
        <div className="flex space-x-2">
          {['1h', '24h', '7d', '30d'].map((range) => (
            <Button
              key={range}
              variant={timeRange === range ? 'default' : 'outline'}
              size="sm"
              onClick={() => setTimeRange(range)}
            >
              {range}
            </Button>
          ))}
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-primary/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">High Risk Users</p>
                <p className="text-2xl font-bold text-cyber-red">3</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-cyber-red" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-primary/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Anomalies Detected</p>
                <p className="text-2xl font-bold text-cyber-orange">23</p>
              </div>
              <Activity className="h-8 w-8 text-cyber-orange" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-primary/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Users</p>
                <p className="text-2xl font-bold text-primary">147</p>
              </div>
              <User className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-primary/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Risk Score</p>
                <p className="text-2xl font-bold text-cyber-green">34</p>
              </div>
              <TrendingUp className="h-8 w-8 text-cyber-green" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* User List */}
        <Card className="border-primary/20">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5 text-primary" />
              <span>User Risk Profiles</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {users.map((user) => (
              <div
                key={user.id}
                className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
                  selectedUser === user.id 
                    ? 'border-primary bg-primary/10' 
                    : 'border-border/50 bg-muted/20 hover:bg-muted/40'
                }`}
                onClick={() => setSelectedUser(user.id)}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    {getDepartmentIcon(user.department)}
                    <span className="font-medium">{user.username}</span>
                  </div>
                  <Badge className={`${getRiskColor(user.riskScore)} border-current`} variant="outline">
                    {user.riskScore}
                  </Badge>
                </div>
                <div className="text-xs text-muted-foreground mb-2">
                  {user.department} • {user.lastSeen}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-xs">Risk Score</span>
                  <span className="text-xs">{user.anomalies} anomalies</span>
                </div>
                <Progress value={user.riskScore} className="h-1 mt-1" />
              </div>
            ))}
          </CardContent>
        </Card>

        {/* User Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* User Profile */}
          <Card className="border-primary/20">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-2">
                  {getDepartmentIcon(selectedUserData.department)}
                  <span>{selectedUserData.username}</span>
                </CardTitle>
                <Badge className={`${getRiskColor(selectedUserData.riskScore)} text-lg px-3 py-1`} variant="outline">
                  Risk: {selectedUserData.riskScore}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className="text-center">
                  <p className="text-sm text-muted-foreground">Department</p>
                  <p className="font-medium">{selectedUserData.department}</p>
                </div>
                <div className="text-center">
                  <p className="text-sm text-muted-foreground">Last Seen</p>
                  <p className="font-medium">{selectedUserData.lastSeen}</p>
                </div>
                <div className="text-center">
                  <p className="text-sm text-muted-foreground">Anomalies</p>
                  <p className="font-medium text-cyber-orange">{selectedUserData.anomalies}</p>
                </div>
                <div className="text-center">
                  <p className="text-sm text-muted-foreground">Session Time</p>
                  <p className="font-medium">{selectedUserData.normalBehavior.avgSessionDuration}</p>
                </div>
              </div>

              {/* Normal Behavior Profile */}
              <div className="space-y-4">
                <h4 className="font-semibold text-cyber-green">Normal Behavior Profile</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Login Times</span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {selectedUserData.normalBehavior.loginTimes.join(', ')}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Common IPs</span>
                    </div>
                    <div className="text-xs text-muted-foreground font-mono">
                      {selectedUserData.normalBehavior.commonIPs.join(', ')}
                    </div>
                  </div>
                  <div className="space-y-2 md:col-span-2">
                    <div className="flex items-center space-x-2">
                      <Terminal className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Common Commands</span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {selectedUserData.normalBehavior.commonCommands.map((cmd) => (
                        <Badge key={cmd} variant="outline" className="text-xs font-mono">
                          {cmd}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card className="border-primary/20">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5 text-primary" />
                <span>Recent Anomalous Activity</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {selectedUserData.recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 rounded-lg bg-muted/20 border border-border/30">
                    <Badge className={`${getRiskBadgeColor(activity.risk)} text-black font-medium mt-1`}>
                      {activity.risk.toUpperCase()}
                    </Badge>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium">{activity.action}</span>
                        <span className="text-xs text-muted-foreground">
                          {new Date(activity.timestamp).toLocaleString()}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">{activity.details}</p>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-4 pt-4 border-t border-border/50">
                <Button variant="cyber" className="w-full">
                  Generate Detailed Report
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};