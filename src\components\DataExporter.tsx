import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Download, 
  Database, 
  FileText, 
  Activity, 
  Shield, 
  Users, 
  Monitor,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface ExportConfig {
  format: 'json' | 'csv' | 'xml';
  includeTimestamp: boolean;
  dataTypes: {
    threats: boolean;
    logs: boolean;
    userBehavior: boolean;
    systemMetrics: boolean;
    honeypotStatus: boolean;
    networkTraffic: boolean;
  };
  timeRange: '1h' | '6h' | '24h' | '7d' | 'all';
}

interface ExportStatus {
  isExporting: boolean;
  progress: number;
  lastExport?: Date;
  exportCount: number;
}

export const DataExporter = () => {
  const [config, setConfig] = useState<ExportConfig>({
    format: 'json',
    includeTimestamp: true,
    dataTypes: {
      threats: true,
      logs: true,
      userBehavior: false,
      systemMetrics: true,
      honeypotStatus: true,
      networkTraffic: false
    },
    timeRange: '24h'
  });

  const [status, setStatus] = useState<ExportStatus>({
    isExporting: false,
    progress: 0,
    exportCount: 0
  });

  const [autoExport, setAutoExport] = useState(false);
  const [exportInterval, setExportInterval] = useState<number>(300); // 5 minutes

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (autoExport) {
      interval = setInterval(() => {
        handleExport();
      }, exportInterval * 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoExport, exportInterval, config]);

  const generateMockData = () => {
    const now = new Date();
    const getTimeAgo = (hours: number) => new Date(now.getTime() - hours * 60 * 60 * 1000);

    return {
      metadata: {
        exportTime: now.toISOString(),
        format: config.format,
        timeRange: config.timeRange,
        source: 'Threat Haven Synergy',
        version: '1.0.0'
      },
      threats: config.dataTypes.threats ? Array.from({ length: 50 }, (_, i) => ({
        id: `threat_${i}`,
        timestamp: getTimeAgo(Math.random() * 24).toISOString(),
        severity: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)],
        type: ['SSH Brute Force', 'Port Scan', 'SQL Injection', 'Malware Upload'][Math.floor(Math.random() * 4)],
        source: `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
        honeypot: ['SSH-HP-01', 'HTTP-HP-02', 'FTP-HP-03'][Math.floor(Math.random() * 3)],
        blocked: Math.random() > 0.3
      })) : [],
      logs: config.dataTypes.logs ? Array.from({ length: 100 }, (_, i) => ({
        id: `log_${i}`,
        timestamp: getTimeAgo(Math.random() * 24).toISOString(),
        level: ['INFO', 'WARN', 'ERROR', 'CRITICAL'][Math.floor(Math.random() * 4)],
        service: ['cowrie', 'dionaea', 'snort', 'suricata'][Math.floor(Math.random() * 4)],
        message: 'System event logged',
        source: ['SSH-HP-01', 'HTTP-HP-02', 'FTP-HP-03'][Math.floor(Math.random() * 3)]
      })) : [],
      systemMetrics: config.dataTypes.systemMetrics ? {
        cpu: {
          usage: Math.random() * 100,
          temperature: 45 + Math.random() * 20,
          frequency: 3600 + Math.random() * 400
        },
        memory: {
          total: 32768,
          used: 16384 + Math.random() * 8192,
          usage: 50 + Math.random() * 30
        },
        disk: {
          total: 1024,
          used: 512 + Math.random() * 256,
          usage: 50 + Math.random() * 30
        },
        network: {
          bytesIn: Math.floor(Math.random() * 1000000),
          bytesOut: Math.floor(Math.random() * 500000)
        }
      } : null,
      honeypotStatus: config.dataTypes.honeypotStatus ? [
        {
          id: 'ssh-01',
          name: 'SSH Honeypot Alpha',
          status: 'online',
          interactions: Math.floor(Math.random() * 1000),
          threats: Math.floor(Math.random() * 500)
        },
        {
          id: 'http-01',
          name: 'Web Honeypot Beta',
          status: 'online',
          interactions: Math.floor(Math.random() * 2000),
          threats: Math.floor(Math.random() * 800)
        }
      ] : [],
      userBehavior: config.dataTypes.userBehavior ? [
        {
          userId: 'user1',
          username: 'john.doe',
          riskScore: Math.floor(Math.random() * 100),
          lastActivity: getTimeAgo(Math.random() * 6).toISOString(),
          anomalies: Math.floor(Math.random() * 10)
        }
      ] : []
    };
  };

  const convertToFormat = (data: any, format: string) => {
    switch (format) {
      case 'csv':
        // Simple CSV conversion for threats
        if (data.threats && data.threats.length > 0) {
          const headers = Object.keys(data.threats[0]).join(',');
          const rows = data.threats.map((threat: any) => 
            Object.values(threat).map(v => `"${v}"`).join(',')
          ).join('\n');
          return `${headers}\n${rows}`;
        }
        return 'No threat data available for CSV export';
      
      case 'xml':
        return `<?xml version="1.0" encoding="UTF-8"?>
<export>
  <metadata>
    <exportTime>${data.metadata.exportTime}</exportTime>
    <source>${data.metadata.source}</source>
  </metadata>
  <threats count="${data.threats?.length || 0}">
    ${data.threats?.map((threat: any) => `
    <threat>
      <id>${threat.id}</id>
      <timestamp>${threat.timestamp}</timestamp>
      <severity>${threat.severity}</severity>
      <type>${threat.type}</type>
      <source>${threat.source}</source>
    </threat>`).join('') || ''}
  </threats>
</export>`;
      
      default:
        return JSON.stringify(data, null, 2);
    }
  };

  const handleExport = async () => {
    setStatus(prev => ({ ...prev, isExporting: true, progress: 0 }));

    // Simulate export progress
    for (let i = 0; i <= 100; i += 10) {
      setStatus(prev => ({ ...prev, progress: i }));
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    const data = generateMockData();
    const content = convertToFormat(data, config.format);
    
    const blob = new Blob([content], { 
      type: config.format === 'json' ? 'application/json' : 
           config.format === 'csv' ? 'text/csv' : 'application/xml'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `threat_data_export_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.${config.format}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    setStatus(prev => ({ 
      ...prev, 
      isExporting: false, 
      progress: 100,
      lastExport: new Date(),
      exportCount: prev.exportCount + 1
    }));

    setTimeout(() => {
      setStatus(prev => ({ ...prev, progress: 0 }));
    }, 2000);
  };

  const dataTypeOptions = [
    { key: 'threats', label: 'Threat Events', icon: Shield, color: 'text-cyber-red' },
    { key: 'logs', label: 'System Logs', icon: FileText, color: 'text-cyber-blue' },
    { key: 'systemMetrics', label: 'System Metrics', icon: Monitor, color: 'text-cyber-green' },
    { key: 'honeypotStatus', label: 'Honeypot Status', icon: Database, color: 'text-cyber-purple' },
    { key: 'userBehavior', label: 'User Behavior', icon: Users, color: 'text-cyber-orange' },
    { key: 'networkTraffic', label: 'Network Traffic', icon: Activity, color: 'text-primary' }
  ];

  return (
    <Card className="border-primary/20">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Download className="h-6 w-6 text-primary" />
          <span>Real-time Data Export</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Export Status */}
        <div className="flex items-center justify-between p-4 bg-muted/20 rounded-lg">
          <div className="flex items-center space-x-3">
            {status.isExporting ? (
              <AlertCircle className="h-5 w-5 text-cyber-orange animate-spin" />
            ) : (
              <CheckCircle className="h-5 w-5 text-cyber-green" />
            )}
            <div>
              <p className="font-medium">
                {status.isExporting ? 'Exporting...' : 'Ready to Export'}
              </p>
              <p className="text-sm text-muted-foreground">
                {status.lastExport 
                  ? `Last export: ${status.lastExport.toLocaleTimeString()}`
                  : 'No exports yet'
                } • Total exports: {status.exportCount}
              </p>
            </div>
          </div>
          {status.isExporting && (
            <div className="text-right">
              <p className="text-sm font-medium">{status.progress}%</p>
              <div className="w-24 h-2 bg-muted rounded-full overflow-hidden">
                <div 
                  className="h-full bg-primary transition-all duration-300"
                  style={{ width: `${status.progress}%` }}
                />
              </div>
            </div>
          )}
        </div>

        {/* Export Configuration */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h3 className="font-semibold">Export Format</h3>
            <Select value={config.format} onValueChange={(value: any) => 
              setConfig(prev => ({ ...prev, format: value }))
            }>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="json">JSON</SelectItem>
                <SelectItem value="csv">CSV</SelectItem>
                <SelectItem value="xml">XML</SelectItem>
              </SelectContent>
            </Select>

            <h3 className="font-semibold">Time Range</h3>
            <Select value={config.timeRange} onValueChange={(value: any) => 
              setConfig(prev => ({ ...prev, timeRange: value }))
            }>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1h">Last Hour</SelectItem>
                <SelectItem value="6h">Last 6 Hours</SelectItem>
                <SelectItem value="24h">Last 24 Hours</SelectItem>
                <SelectItem value="7d">Last 7 Days</SelectItem>
                <SelectItem value="all">All Data</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-4">
            <h3 className="font-semibold">Data Types</h3>
            <div className="space-y-3">
              {dataTypeOptions.map((option) => (
                <div key={option.key} className="flex items-center space-x-3">
                  <Checkbox
                    checked={config.dataTypes[option.key as keyof typeof config.dataTypes]}
                    onCheckedChange={(checked) => 
                      setConfig(prev => ({
                        ...prev,
                        dataTypes: {
                          ...prev.dataTypes,
                          [option.key]: checked
                        }
                      }))
                    }
                  />
                  <option.icon className={`h-4 w-4 ${option.color}`} />
                  <span className="text-sm">{option.label}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Auto Export */}
        <div className="border-t pt-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Checkbox
                checked={autoExport}
                onCheckedChange={setAutoExport}
              />
              <Clock className="h-4 w-4 text-primary" />
              <span className="font-medium">Auto Export</span>
            </div>
            {autoExport && (
              <Select value={exportInterval.toString()} onValueChange={(value) => 
                setExportInterval(parseInt(value))
              }>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="60">1 minute</SelectItem>
                  <SelectItem value="300">5 minutes</SelectItem>
                  <SelectItem value="900">15 minutes</SelectItem>
                  <SelectItem value="3600">1 hour</SelectItem>
                </SelectContent>
              </Select>
            )}
          </div>
        </div>

        {/* Export Button */}
        <Button 
          onClick={handleExport} 
          disabled={status.isExporting}
          className="w-full"
          size="lg"
        >
          <Download className="h-4 w-4 mr-2" />
          {status.isExporting ? 'Exporting...' : 'Export Now'}
        </Button>
      </CardContent>
    </Card>
  );
};
