import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { useRealTimeExport } from '@/hooks/useRealTimeExport';
import { 
  Activity, 
  Download, 
  Clock, 
  Database, 
  Zap, 
  AlertCircle, 
  CheckCircle,
  BarChart3,
  Settings,
  Play,
  Pause
} from 'lucide-react';

export const RealTimeExportMonitor = () => {
  const {
    config,
    stats,
    exportQueue,
    updateConfig,
    manualExport,
    resetStats,
    isActive
  } = useRealTimeExport();

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Activity className="h-6 w-6 text-primary" />
          <h2 className="text-2xl font-bold">Real-Time Export Monitor</h2>
          <Badge className={`${isActive ? 'bg-cyber-green' : 'bg-muted'} text-black font-semibold`}>
            {isActive ? (
              <>
                <Zap className="h-3 w-3 mr-1" />
                ACTIVE
              </>
            ) : (
              <>
                <Pause className="h-3 w-3 mr-1" />
                INACTIVE
              </>
            )}
          </Badge>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={resetStats}>
            Reset Stats
          </Button>
          <Button onClick={manualExport} disabled={stats.isExporting}>
            <Download className="h-4 w-4 mr-2" />
            Export Now
          </Button>
        </div>
      </div>

      {/* Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-primary/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Exports</CardTitle>
            <Database className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalExports}</div>
            <p className="text-xs text-muted-foreground">
              {stats.lastExportTime 
                ? `Last: ${stats.lastExportTime.toLocaleTimeString()}`
                : 'No exports yet'
              }
            </p>
          </CardContent>
        </Card>

        <Card className="border-primary/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Data Exported</CardTitle>
            <BarChart3 className="h-4 w-4 text-cyber-blue" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatBytes(stats.totalDataExported)}</div>
            <p className="text-xs text-muted-foreground">
              Avg: {stats.totalExports > 0 ? formatBytes(stats.totalDataExported / stats.totalExports) : '0 B'} per export
            </p>
          </CardContent>
        </Card>

        <Card className="border-primary/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Queue Size</CardTitle>
            <Clock className="h-4 w-4 text-cyber-orange" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{exportQueue}</div>
            <p className="text-xs text-muted-foreground">
              {stats.isExporting ? 'Processing...' : 'Waiting'}
            </p>
          </CardContent>
        </Card>

        <Card className="border-primary/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Status</CardTitle>
            {stats.errors > 0 ? (
              <AlertCircle className="h-4 w-4 text-cyber-red" />
            ) : (
              <CheckCircle className="h-4 w-4 text-cyber-green" />
            )}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-cyber-green">
              {stats.errors === 0 ? 'Healthy' : `${stats.errors} Errors`}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats.isExporting ? 'Exporting...' : 'Ready'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Configuration */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="border-primary/20">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-primary" />
              <span>Export Configuration</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Enable Real-Time Export</label>
              <Switch
                checked={config.enabled}
                onCheckedChange={(enabled) => updateConfig({ enabled })}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Export Interval</label>
              <Select 
                value={config.interval.toString()} 
                onValueChange={(value) => updateConfig({ interval: parseInt(value) })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="30">30 seconds</SelectItem>
                  <SelectItem value="60">1 minute</SelectItem>
                  <SelectItem value="300">5 minutes</SelectItem>
                  <SelectItem value="900">15 minutes</SelectItem>
                  <SelectItem value="3600">1 hour</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Export Format</label>
              <Select 
                value={config.format} 
                onValueChange={(format: any) => updateConfig({ format })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="json">JSON</SelectItem>
                  <SelectItem value="csv">CSV</SelectItem>
                  <SelectItem value="xml">XML</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Destination</label>
              <Select 
                value={config.destination} 
                onValueChange={(destination: any) => updateConfig({ destination })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="download">Download</SelectItem>
                  <SelectItem value="websocket">WebSocket</SelectItem>
                  <SelectItem value="api">API Endpoint</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Max File Size (MB)</label>
              <Select 
                value={config.maxFileSize.toString()} 
                onValueChange={(size) => updateConfig({ maxFileSize: parseInt(size) })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 MB</SelectItem>
                  <SelectItem value="5">5 MB</SelectItem>
                  <SelectItem value="10">10 MB</SelectItem>
                  <SelectItem value="25">25 MB</SelectItem>
                  <SelectItem value="50">50 MB</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        <Card className="border-primary/20">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-cyber-blue" />
              <span>Export Statistics</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Export Success Rate</span>
                <span className="text-sm font-medium">
                  {stats.totalExports > 0 
                    ? `${(((stats.totalExports - stats.errors) / stats.totalExports) * 100).toFixed(1)}%`
                    : '0%'
                  }
                </span>
              </div>
              <Progress 
                value={stats.totalExports > 0 ? ((stats.totalExports - stats.errors) / stats.totalExports) * 100 : 0} 
                className="h-2"
              />
            </div>

            <div className="grid grid-cols-2 gap-4 pt-4 border-t">
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Successful</p>
                <p className="text-lg font-bold text-cyber-green">{stats.totalExports - stats.errors}</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Failed</p>
                <p className="text-lg font-bold text-cyber-red">{stats.errors}</p>
              </div>
            </div>

            <div className="pt-4 border-t">
              <div className="text-center">
                <p className="text-sm text-muted-foreground">Average Export Size</p>
                <p className="text-lg font-bold">
                  {stats.totalExports > 0 
                    ? formatBytes(stats.totalDataExported / stats.totalExports)
                    : '0 B'
                  }
                </p>
              </div>
            </div>

            {stats.isExporting && (
              <div className="pt-4 border-t">
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                  <span className="text-sm">Exporting data...</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Real-time Activity Feed */}
      <Card className="border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5 text-primary" />
            <span>Real-Time Activity</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {isActive ? (
              <div className="text-center py-8">
                <div className="animate-pulse flex items-center justify-center space-x-2">
                  <Zap className="h-5 w-5 text-primary" />
                  <span className="text-sm text-muted-foreground">
                    Real-time data collection active...
                  </span>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Next export in {config.interval} seconds
                </p>
              </div>
            ) : (
              <div className="text-center py-8">
                <Pause className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">
                  Real-time export is disabled
                </p>
                <p className="text-xs text-muted-foreground">
                  Enable to start collecting and exporting data
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
